{"name": "short-video-factory", "description": "短视频工厂，一键生成产品营销与泛内容短视频，AI批量自动剪辑", "version": "0.0.1", "author": {"name": "YILS", "developer": "YILS", "email": "<EMAIL>", "url": "https://yils.blog/"}, "main": "dist-electron/main.js", "scripts": {"dev": "cross-env VITE_CJS_IGNORE_WARNING=true vite", "build": "vue-tsc && cross-env VITE_CJS_IGNORE_WARNING=true vite build && electron-builder", "preview": "vite preview", "format": "prettier --write .", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@vueuse/core": "^13.5.0", "better-sqlite3": "9.6.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuetify": "^3.9.0"}, "devDependencies": {"@mdi/font": "^7.4.47", "@types/better-sqlite3": "^7.6.13", "@vitejs/plugin-vue": "^6.0.0", "cross-env": "^7.0.3", "electron": "^22.3.27", "electron-builder": "^24.13.3", "prettier": "^3.6.2", "sass": "^1.89.2", "typescript": "5.6.2", "unocss": "^66.3.3", "vite": "^7.0.3", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "3.0.1"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=22.17.0", "pnpm": ">=10.12.4"}}