# npm源地址
registry=https://registry.npmmirror.com/
# node-sass依赖
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
# weex项目依赖phantomjs-prebuilt
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
# electron依赖
# 阿里云采用双份拷贝策略,即x.x版本的electron存储时既有 vx.x也有x.x
electron_mirror=https://npmmirror.com/mirrors/electron/
electron_custom_dir={{ version }}
# sqlite3预构建二进制
sqlite3_binary_host_mirror=http://npmmirror.com/mirrors/
# better-sqlite3预构建二进制
better_sqlite3_binary_host=https://registry.npmmirror.com/-/binary/better-sqlite3
# node-inspector依赖
profiler_binary_host_mirror=http://npmmirror.com/mirrors/node-inspector/
# chromedriver安装失败
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver
# sentry-cli依赖
sentrycli_cdnurl=https://npmmirror.com/mirrors/sentry-cli/
# 平铺依赖，以便electron-builder依赖分析与打包
shamefully-hoist=true
engine-strict = true
